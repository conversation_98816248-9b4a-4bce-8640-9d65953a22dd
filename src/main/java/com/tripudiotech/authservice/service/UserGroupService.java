/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.authservice.constant.SearchingParameterConstants;
import com.tripudiotech.authservice.request.GroupRequest;
import com.tripudiotech.authservice.response.UserGroupResponse;
import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.util.TokenUtils;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.db.query.ConditionKeyword;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.dto.UserGroupInformation;
import com.tripudiotech.securitylib.dto.UserRealmInformationResponse;
import com.tripudiotech.securitylib.dto.request.UserGroupRequest;
import com.tripudiotech.securitylib.dto.response.ErrorResponseDTO;
import com.tripudiotech.securitylib.service.provider.SecurityProviderService;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.ws.rs.core.GenericType;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.json.JSONObject;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.MultivaluedHashMap;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.tripudiotech.authservice.constant.EntityTypeConstants.PERSON_ENTITY_TYPE;

/**
 * <AUTHOR>
 */
@Slf4j
@ApplicationScoped
public class UserGroupService {
    private static final String LOG_PREFIX = "[UserGroupService]";
    @Inject
    @RestClient
    EntityServiceClient entityRepository;
    @Inject
    JsonWebToken jsonWebToken;
    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;
    @Inject
    UserService userService;
    @Inject
    ObjectMapper objectMapper;

    static final String USER_GROUP = "UserGroup";

    public Uni<UserGroupResponse> create(@NonNull String tenantId,
                                            @NonNull GroupRequest request) {
        if (request.getProperties().isEmpty() || !request.getProperties().containsKey(DBConstants.NAME_PROPERTY)) {
            return Uni.createFrom().failure(new BadRequestException(tenantId, "Missing name properties"));
        }

        return Uni.createFrom().item(() -> {
                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                    String authServerGeneratedId;
                    List<String> locationSplits;

                    try (Response response = securityProviderService
                            .createUserGroup(tenantId, UserGroupRequest.builder()
                                    .groupType("Group")
                                    .name(request.getProperties().get(DBConstants.NAME_PROPERTY).toString())
                                    .description(Optional.ofNullable(request.getProperties().get(DBConstants.DESCRIPTION_PROPERTY)).map(Objects::toString)
                                            .orElse(null))
                                    .build())) {
                        if (!Response.Status.Family.familyOf(response.getStatus()).equals(Response.Status.Family.SUCCESSFUL)) {
                            response.bufferEntity();
                            if (response.getStatus() == Response.Status.CONFLICT.getStatusCode()) {
                                authServerGeneratedId = securityProviderService
                                        .searchUserGroup(tenantId, request.getProperties()
                                                .get(DBConstants.NAME_PROPERTY).toString(), 0, 1).get(0).getId();

                                return Tuple2.of(TokenUtils.addBearerPrefix(jsonWebToken.getRawToken()), authServerGeneratedId);
                            }
                            log.error("{} Failed to create user group in keycloak. Response Status: {}", LOG_PREFIX, response.getStatus());
                            throw new ServiceException(tenantId,
                                    BusinessErrorCode.fromHttpStatusCode(response.getStatus()),
                                    Optional.ofNullable(response.readEntity(ErrorResponseDTO.class).getErrorMessage())
                                            .map(Object::toString).orElseGet(() -> response.readEntity(String.class)));
                        }
                        locationSplits = Optional.ofNullable(response.getHeaderString("location"))
                                .map(location -> location.split("/"))
                                .map(Arrays::asList)
                                .orElse(Collections.emptyList());
                    }

                    if (locationSplits.isEmpty()) {
                        log.error("{} Unable to get user group id in keycloak", LOG_PREFIX);
                        throw new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR, "Failed to create an User Group");
                    }

                    authServerGeneratedId = locationSplits.get(locationSplits.size() - 1);

                    return Tuple2.of(TokenUtils.addBearerPrefix(jsonWebToken.getRawToken()), authServerGeneratedId);
                }).runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .flatMap(tuple -> {
                    String token = tuple.getItem1();
                    String authServerGeneratedId = tuple.getItem2();

                    // Check if this is an existing group (from 409 conflict)
                    if (authServerGeneratedId != null && request.getProperties().get(DBConstants.NAME_PROPERTY) != null) {
                            return getById(tenantId, authServerGeneratedId)
                                    .map(entity -> new UserGroupResponse(authServerGeneratedId, entity));
                    }

                    Map<String, Object> attributes = new HashMap<>(request.getProperties());
                    attributes.put(DBConstants.AUTH_ID_PROPERTY, authServerGeneratedId);

                    return entityRepository.createEntity(
                                    token,
                                    tenantId,
                                    USER_GROUP,
                                    CreateEntityRequest.builder()
                                            .relations(new HashSet<>())
                                            .attributes(attributes)
                                            .build())
                            .map(response -> response.readEntity(EntityWithPermission.class))
                            .map(createdTeamResponse -> new UserGroupResponse(authServerGeneratedId, createdTeamResponse))
                            .onFailure().invoke(exception -> {
                                log.error("Rolling back data, deleting new created group in keycloak. Name: {}",
                                        request.getProperties().get(DBConstants.NAME_PROPERTY).toString());
                                // Rollback: delete the created group in keycloak
                                Optional.ofNullable(authServerGeneratedId).filter(StringUtils::isNoneBlank)
                                        .ifPresent(generatedId -> {
                                            try {
                                                securityProviderServiceFactory.getDefaultAuthenticateService().removeUserGroup(tenantId, generatedId);
                                                log.info("Removed user group key cloak id: {}", generatedId);
                                            } catch (Exception rollbackException) {
                                                log.error("Failed to rollback user group in keycloak. GroupId: {}, Error: {}",
                                                        generatedId, rollbackException.getMessage(), rollbackException);
                                            }
                                        });

                            });
                });
    }


    public Uni<EntityWithPermission> update(String tenantId, String entityId, GroupRequest request) {
        if (request.getProperties() == null || request.getProperties().isEmpty()) {
            throw new BadRequestException(tenantId, "The required field is empty");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP, entityId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(entity -> {
                    String authId = Optional.ofNullable(entity.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                            .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                                log.error("{} Could not find authId property for group Id {}", LOG_PREFIX, entityId);
                                return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                            });

                    return Uni.createFrom().item(() -> {
                                SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                UserGroupInformation existingUserGroup = securityProviderService.getUserGroupById(tenantId, authId);
                                return Tuple2.of(entity, existingUserGroup);
                            })
                            .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                            .flatMap(tuple -> {
                                EntityWithPermission originalEntity = tuple.getItem1();
                                UserGroupInformation existingUserGroup = tuple.getItem2();

                                return entityRepository.updateEntity(token, tenantId, entityId,
                                                UpdateEntityRequest.builder()
                                                        .attributes(request.getProperties())
                                                        .build())
                                        .map(response -> response.readEntity(EntityWithPermission.class))
                                        .flatMap(updatedEntity -> {
                                            // Do update group name in keycloak
                                            return Uni.createFrom().item(() -> {
                                                        String newGroupName = Optional.of(request.getProperties())
                                                                .map(value -> value.get(DBConstants.NAME_PROPERTY))
                                                                .map(Object::toString).filter(StringUtils::isNoneBlank).orElse(null);
                                                        if (!StringUtils.isBlank(newGroupName) &&
                                                            !newGroupName.equals(existingUserGroup.getName())) {
                                                            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                                            securityProviderService.updateUserGroup(tenantId, authId, newGroupName);
                                                        } else {
                                                            log.info("{} Properties.name is empty or the same with old group name, ignore update in keycloak", LOG_PREFIX);
                                                        }
                                                        return updatedEntity;
                                                    })
                                                    .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                                                    .onFailure().recoverWithUni(e -> {
                                                        log.error("{} Failed to updated group name in keycloak, rolling back the data in neo4j", LOG_PREFIX, e);
                                                        Map<String, Object> originalProperties = originalEntity.getProperties();
                                                        return entityRepository.updateEntity(token, tenantId, entityId,
                                                                        UpdateEntityRequest.builder()
                                                                                .attributes(originalProperties)
                                                                                .build())
                                                                .map(response -> response.readEntity(EntityWithPermission.class))
                                                                .onItem().failWith(() -> e);
                                                    });
                                        });
                            });
                });
    }

    public Uni<PageResponse<EntityWithPermission>> searchGroups(
            @NonNull String tenantId,
            String query,
            @NonNull Integer offset,
            @NonNull Integer limit
    ) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        return entityRepository.getAll(token, tenantId, USER_GROUP, offset, limit, query, null, null, null, null)
                .map(response ->
                        response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {
                        }));
    }


    public Uni<Void> removeGroup(
            @NonNull String tenantId,
            @NonNull String groupId) {

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP, groupId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(teamEntityWithPermission -> {
                    String authId = Optional.ofNullable(teamEntityWithPermission.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                            .map(Object::toString).orElse(null);

                    return entityRepository.deleteEntity(token, tenantId, groupId)
                            .replaceWithVoid()
                            .onItem().invoke(() -> {
                                if (!StringUtils.isBlank(authId)) {
                                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                    securityProviderService.removeUserGroup(tenantId, authId);
                                } else {
                                    log.info("{} Could not found authId. Skip delete UserGroup in keycloak", LOG_PREFIX);
                                }
                            })
                            .runSubscriptionOn(Infrastructure.getDefaultWorkerPool());
                });
    }


    public Uni<Void> addUserToGroup(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull String userEmail) {
        if (userEmail.isBlank()) {
            throw new BadRequestException(tenantId, "User email must be non-blank");
        }
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP, groupId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(entity -> {
                    String authId = Optional.ofNullable(entity.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                            .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                                log.error("{} Could not find authId property for group Id {}", LOG_PREFIX, groupId);
                                return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                            });

                    return userService.getUserEntityByEmail(tenantId, userEmail)
                            .flatMap(addedUser -> {
                                return Uni.createFrom().item(() -> {
                                    SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                    MultivaluedMap<String, String> queryParams = new MultivaluedHashMap<>();
                                    queryParams.put(SearchingParameterConstants.EMAIL, List.of(userEmail.trim()));
                                    List<UserRealmInformationResponse> userResponseList = securityProviderService.searchUsers(tenantId, queryParams);
                                    if (userResponseList.isEmpty()) {
                                        throw new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail);
                                    }
                                    UserRealmInformationResponse userRealmInformationResponse = userResponseList.stream()
                                            .filter(data -> data.getEmail().equals(userEmail))
                                            .findFirst()
                                            .orElseThrow(() -> new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail));

                                    // Complete add user to group in keycloak
                                    securityProviderService.addUserToGroup(tenantId, authId, userRealmInformationResponse.getId());
                                    log.info("{} Add user to group in keycloak success. Email: {}, GroupId: {}", LOG_PREFIX, userEmail, authId);

                                    return null; // Return void
                                }).runSubscriptionOn(Infrastructure.getDefaultWorkerPool()).replaceWithVoid();
                            });
                })
                .replaceWithVoid();
    }

    public Uni<Void> removeUserFromGroup(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull String userEmail) {
        if (userEmail.isBlank()) {
            throw new BadRequestException(tenantId, "User email must be non-blank");
        }
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP, groupId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(entity -> {
                    String authId = Optional.ofNullable(entity.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                            .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                                log.error("{} Could not find authServerId property for group Id {}", LOG_PREFIX, groupId);
                                return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                            });

                    return userService.getUserEntityByEmail(tenantId, userEmail)
                            .flatMap(userInfo -> {
                                return Uni.createFrom().item(() -> {
                                            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                            MultivaluedMap<String, String> queryParams = new MultivaluedHashMap<>();
                                            queryParams.put(SearchingParameterConstants.EMAIL, List.of(userEmail.trim()));
                                            List<UserRealmInformationResponse> userResponseList = securityProviderService.searchUsers(tenantId, queryParams);
                                            if (userResponseList.isEmpty()) {
                                                throw new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail);
                                            }

                                            UserRealmInformationResponse userRealmInformationResponse = userResponseList.stream()
                                                    .filter(data -> data.getEmail().equals(userEmail))
                                                    .findFirst()
                                                    .orElseThrow(() -> new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail));

                                            // Complete remove user from group in keycloak
                                            securityProviderService.removeUserToTheGroup(tenantId, authId, userRealmInformationResponse.getId());
                                            log.info("{} Remove user from group in keycloak success. Email: {}, GroupId: {}", LOG_PREFIX, userEmail, authId);

                                            return userRealmInformationResponse;
                                        })
                                        .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                                        .flatMap(userRealmInformationResponse -> {
                                            return entityRepository.deleteRelation(
                                                            token, tenantId, userInfo.getId(), DBConstants.RELATION_WORKS_FOR, groupId)
                                                    .replaceWithVoid()
                                                    .onItem().invoke(() ->
                                                            log.info("{} Delete WORKS_FOR relationship success. UserId: {}, GroupId: {}", LOG_PREFIX,
                                                                    userInfo.getId(), groupId))
                                                    .onFailure().invoke(exception -> {
                                                        log.error("{} Failed to delete relation between group and user. GroupId: {}, UserId: {}", LOG_PREFIX,
                                                                groupId, userInfo.getId(), exception);
                                                        // Rollback: add user back to group in keycloak
                                                        try {
                                                            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                                            securityProviderService.addUserToGroup(tenantId, authId, userRealmInformationResponse.getId());
                                                        } catch (Exception rollbackException) {
                                                            log.error("{} Failed to rollback user to group in keycloak. GroupId: {}, UserId: {}", LOG_PREFIX,
                                                                    groupId, userInfo.getId(), rollbackException);
                                                        }
                                                    })
                                                    .onFailure().transform(exception ->
                                                            new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR));
                                        });
                            });
                });
    }


    public Uni<PageResponse<?>> getUsersInGroupBy(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull Integer offset,
            @NonNull Integer limit
    ) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        return entityRepository.getEntitiesUnderRelationType(
                token,
                tenantId,
                offset,
                limit,
                groupId,
                DBConstants.RELATION_WORKS_FOR,
                PERSON_ENTITY_TYPE,
                true
        ).map(response -> response.readEntity(PageResponse.class));
    }

    /**
     * Find a group by name, create it if it doesn't exist
     *
     * @param tenantId  the tenant ID
     * @param groupName the name of the group to find or create
     * @return the group entity
     */
    public Uni<EntityWithPermission> findOrCreateGroupByName(@NonNull String tenantId, @NonNull String groupName) {
        if (StringUtils.isBlank(groupName)) {
            throw new BadRequestException(tenantId, "Group name cannot be empty or blank");
        }
        // Search for existing group by name
        JSONObject jsonQuery = new JSONObject()
                .put(
                        ConditionKeyword.EXACT.getValue(),
                        new JSONObject()
                                .put(String.format("%s", DBConstants.NAME_PROPERTY), groupName));

        return searchGroups(tenantId, jsonQuery.toString(), 0, 1).map(pageResponse -> {
            if (pageResponse.getData() != null && !pageResponse.getData().isEmpty()) {
                return pageResponse.getData().get(0);
            }
            return null;
        }).onItem().ifNull().switchTo(() ->
                {
                    // Group doesn't exist, create it
                    log.info("{} Group '{}' not found, creating new group", LOG_PREFIX, groupName);

                    Map<String, Object> groupProperties = new HashMap<>();
                    groupProperties.put(DBConstants.NAME_PROPERTY, groupName);
                    groupProperties.put(DBConstants.DESCRIPTION_PROPERTY, "Auto-created group for user assignment");

                    GroupRequest groupRequest = GroupRequest.builder()
                            .properties(groupProperties)
                            .build();

                    return create(tenantId, groupRequest);
                }
        );
    }

    /**
     * Get a group by ID
     *
     * @param tenantId the tenant ID
     * @param groupId  the ID of the group to find
     * @return the group entity
     * @throws RecordNotFoundException if group is not found
     */
    public Uni<EntityWithPermission> getById(@NonNull String tenantId, @NonNull String groupId) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP, groupId)
                .map(response -> {
                    EntityWithPermission entityWithPermission = response.readEntity(EntityWithPermission.class);

                    if (entityWithPermission == null || StringUtils.isBlank(entityWithPermission.getId())) {
                        throw new RecordNotFoundException(tenantId, "Group not found with ID: " + groupId);
                    }

                    return entityWithPermission;
                });

    }

    /**
     * Find a group by name (without auto-creation)
     *
     * @param tenantId  the tenant ID
     * @param groupName the name of the group to find
     * @return the group entity
     * @throws RecordNotFoundException if group is not found
     */
    public Uni<EntityWithPermission> findGroupByName(@NonNull String tenantId, @NonNull String groupName) {
        if (StringUtils.isBlank(groupName)) {
            throw new BadRequestException(tenantId, "Group name cannot be empty or blank");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        // Search for existing group by name
        String query = new JSONObject().put("$exact", new JSONObject().put(DBConstants.NAME_PROPERTY, groupName)).toString();
        return entityRepository.getAll(token, tenantId, USER_GROUP, 0, 1, query, null, null, null, null)
                .map(response ->
                        response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {
                        }))
                .map(pageResponse -> {
                    if (pageResponse.getData() != null && !pageResponse.getData().isEmpty()) {
                        return pageResponse.getData().get(0);
                    }
                    throw new RecordNotFoundException(tenantId, "Group not found with name: " + groupName);
                });
    }

    /**
     * Find a group by external ID
     *
     * @param tenantId   the tenant ID
     * @param externalId the external ID of the group to find
     * @return the group entity
     * @throws RecordNotFoundException if group is not found
     */
    public Uni<EntityWithPermission> findGroupByExternalId(@NonNull String tenantId, @NonNull String externalId) {
        if (StringUtils.isBlank(externalId)) {
            throw new BadRequestException(tenantId, "External ID cannot be empty or blank");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        // Search for existing group by external ID
        String query = new JSONObject().put("$exact", new JSONObject().put("externalId", externalId)).toString();
        return entityRepository.getAll(token, tenantId, USER_GROUP, 0, 1, query, null, null, null, null)
                .map(response ->
                        response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {
                        }))
                .map(pageResponse -> {
                    if (pageResponse.getData() != null && !pageResponse.getData().isEmpty()) {
                        return pageResponse.getData().get(0);
                    }
                    throw new RecordNotFoundException(tenantId, "Group not found with external ID: " + externalId);
                });
    }

    /**
     * Find a group by code
     *
     * @param tenantId the tenant ID
     * @param code     the code of the group to find
     * @return the group entity
     * @throws RecordNotFoundException if group is not found
     */
    public Uni<EntityWithPermission> findGroupByCode(@NonNull String tenantId, @NonNull String code) {
        if (StringUtils.isBlank(code)) {
            throw new BadRequestException(tenantId, "Group code cannot be empty or blank");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        // Search for existing group by code
        String query = new JSONObject().put("$exact", new JSONObject().put("code", code)).toString();
        return entityRepository.getAll(token, tenantId, USER_GROUP, 0, 1, query, null, null, null, null)
                .map(response ->
                        response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {
                        }))
                .map(pageResponse -> {
                    if (pageResponse.getData() != null && !pageResponse.getData().isEmpty()) {
                        return pageResponse.getData().get(0);
                    }
                    throw new RecordNotFoundException(tenantId, "Group not found with code: " + code);
                });
    }
}
